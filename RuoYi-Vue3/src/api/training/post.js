import request from '@/utils/request'

// 查询岗位模型列表
export function listPost(query) {
  return request({
    url: '/training/post/list',
    method: 'get',
    params: query
  })
}

// 查询岗位模型详细
export function getPost(postId) {
  return request({
    url: '/training/post/' + postId,
    method: 'get'
  })
}

// 新增岗位模型
export function addPost(data) {
  return request({
    url: '/training/post',
    method: 'post',
    data: data
  })
}

// 修改岗位模型
export function updatePost(data) {
  return request({
    url: '/training/post',
    method: 'put',
    data: data
  })
}

// 删除岗位模型
export function delPost(postIds) {
  return request({
    url: '/training/post/remove',
    method: 'post',
    params: { postIds: postIds }
  })
}

// 复制岗位模型
export function copyPost(data) {
  return request({
    url: '/training/post/copy',
    method: 'post',
    data: data
  })
}

// 查询企业列表
export function listCompany() {
  return request({
    url: '/organization/company/list',
    method: 'get',
    params: { pageNum: 1, pageSize: 1000 }
  }).then(response => {
    // 转换为原有格式以保持兼容性
    return {
      data: (response.rows || []).map(company => ({
        id: company.companyId,
        name: company.companyName
      }))
    }
  })
}