<template>
  <div class="app-container">
    <!-- 顶部标题栏 -->
    <div class="page-header">
      <el-button link @click="handleBack" class="back-btn">
        <el-icon><ArrowLeft /></el-icon>
        返回
      </el-button>
      <span class="page-title">{{ title }}</span>
    </div>

    <!-- 基础信息 -->
    <el-card class="section-card">
      <template #header>
        <div class="section-header">
          <span class="section-title">基础信息</span>
        </div>
      </template>
      
      <div v-if="isViewMode" class="view-content">
        <el-row :gutter="40">
          <el-col :span="12">
            <div class="view-item">
              <span class="label">合同编号：</span>
              <span class="value">{{ contractInfo.contractNumber || '-' }}</span>
            </div>
          </el-col>
          <el-col :span="12">
            <div class="view-item">
              <span class="label">合同名称：</span>
              <span class="value">{{ contractInfo.contractName || '-' }}</span>
            </div>
          </el-col>
          <el-col :span="12">
            <div class="view-item">
              <span class="label">签约日期：</span>
              <span class="value">{{ parseTime(contractInfo.signDate, '{y}-{m}-{d}') || '-' }}</span>
            </div>
          </el-col>
          <el-col :span="12">
            <div class="view-item">
              <span class="label">学生姓名：</span>
              <span class="value">{{ contractInfo.studentName || '-' }}</span>
            </div>
          </el-col>
          <el-col :span="12">
            <div class="view-item">
              <span class="label">学号：</span>
              <span class="value">{{ contractInfo.studentNumber || '-' }}</span>
            </div>
          </el-col>
          <el-col :span="12">
            <div class="view-item">
              <span class="label">专业：</span>
              <span class="value">{{ contractInfo.major || '-' }}</span>
            </div>
          </el-col>
          <el-col :span="12">
            <div class="view-item">
              <span class="label">校内教师：</span>
              <span class="value">{{ contractInfo.schoolTeacher || '-' }}</span>
            </div>
          </el-col>
          <el-col :span="12">
            <div class="view-item">
              <span class="label">企业名称：</span>
              <span class="value">{{ contractInfo.companyName || '-' }}</span>
            </div>
          </el-col>
          <el-col :span="12">
            <div class="view-item">
              <span class="label">企业导师：</span>
              <span class="value">{{ contractInfo.companyTeacher || '-' }}</span>
            </div>
          </el-col>
        </el-row>
      </div>

             <div v-else class="form-content">
         <el-form ref="contractRef" :model="form" :rules="rules" label-width="100px">
           <el-row :gutter="40">
             <el-col :span="12">
               <el-form-item label="合同编号" prop="contractNumber" required>
                 <el-input v-model="form.contractNumber" placeholder="请选择" />
               </el-form-item>
             </el-col>
             <el-col :span="12">
               <el-form-item label="合同名称" prop="contractName" required>
                 <el-input v-model="form.contractName" placeholder="请输入" maxlength="20" show-word-limit />
               </el-form-item>
             </el-col>
             <el-col :span="12">
               <el-form-item label="签约日期" prop="signDate">
                 <el-date-picker
                   v-model="form.signDate"
                   type="date"
                   placeholder="请选择日期"
                   style="width: 100%"
                   value-format="YYYY-MM-DD"
                 />
               </el-form-item>
             </el-col>
             <el-col :span="12">
               <el-form-item label="学生姓名" prop="studentId" required>
                 <el-select v-model="form.studentId" placeholder="请输入学生姓名" style="width: 100%">
                   <el-option
                     v-for="student in studentOptions"
                     :key="student.id"
                     :label="`${student.name} (${student.number})`"
                     :value="student.id"
                   />
                 </el-select>
               </el-form-item>
             </el-col>
             <el-col :span="12">
               <el-form-item label="企业名称" prop="companyName" required>
                 <el-select v-model="form.companyName" placeholder="请输入企业名称" style="width: 100%">
                   <el-option
                     v-for="company in companyOptions"
                     :key="company.name"
                     :label="company.name"
                     :value="company.name"
                   />
                 </el-select>
               </el-form-item>
             </el-col>
           </el-row>
         </el-form>
       </div>
    </el-card>

    <!-- 岗位信息 -->
    <el-card class="section-card">
      <template #header>
        <div class="section-header">
          <span class="section-title">岗位信息</span>
        </div>
      </template>
      
      <div v-if="isViewMode" class="view-content">
        <el-row :gutter="40">
          <el-col :span="12">
            <div class="view-item">
              <span class="label">岗位：</span>
              <span class="value">{{ contractInfo.postName || '-' }}</span>
            </div>
          </el-col>
          <el-col :span="12">
            <div class="view-item">
              <span class="label">薪酬：</span>
              <span class="value">{{ contractInfo.salary ? contractInfo.salary + '元' : '-' }}</span>
            </div>
          </el-col>
        </el-row>
      </div>

      <div v-else class="form-content">
        <el-form ref="contractRef2" :model="form" :rules="rules" label-width="100px">
          <el-row :gutter="40">
            <el-col :span="12">
              <el-form-item label="岗位" prop="postName" required>
                <el-select v-model="form.postName" placeholder="请选择" style="width: 100%" allow-create filterable>
                  <el-option
                    v-for="post in postOptions"
                    :key="post.id"
                    :label="post.name"
                    :value="post.name"
                  />
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="薪酬" prop="salary">
                <el-input-number 
                  v-model="form.salary" 
                  :min="0" 
                  :precision="2" 
                  placeholder="0"
                  style="width: 100%"
                />
              </el-form-item>
            </el-col>
          </el-row>
        </el-form>
      </div>
    </el-card>

    <!-- 操作按钮 -->
    <div class="action-footer">
      <el-button @click="handleCancel">取消</el-button>
      <el-button v-if="isViewMode" type="primary" @click="handleConfirm">确认</el-button>
      <el-button v-else type="primary" @click="handleSubmit" :loading="loading">确认</el-button>
    </div>
  </div>
</template>

<script setup name="ContractDialog">
import { ref, reactive, computed, onMounted, getCurrentInstance } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import { ArrowLeft } from '@element-plus/icons-vue';
import { getContract, addContract, updateContract } from "@/api/student/contract";
import { listPost } from "@/api/training/post";
import { listStudentInfo } from "@/api/student/student";
import { listCompany } from "@/api/organization/company";
import { parseTime } from "@/utils/ruoyi";

const route = useRoute();
const router = useRouter();

const { proxy } = getCurrentInstance();

const loading = ref(false);
const contractId = computed(() => route.params.id);
const pageMode = computed(() => {
  if (route.path.includes('/add')) return 'add';
  if (route.path.includes('/edit/')) return 'edit';
  if (route.path.includes('/detail/')) return 'detail';
  return 'add';
});

const isViewMode = ref(false);
const contractInfo = ref({});

const title = computed(() => {
  if (pageMode.value === 'add') return '新增';
  if (pageMode.value === 'edit') return '编辑';
  if (pageMode.value === 'detail') return '查看';
  return '新增';
});

// 表单数据
const form = ref({
  contractId: null,
  contractNumber: '',
  contractName: '',
  signDate: '',
  studentId: null,
  companyName: '',
  postName: '',
  salary: null,
  remark: ''
});

// 表单验证规则
const rules = {
  contractNumber: [
    { required: true, message: "合同编号不能为空", trigger: "blur" },
    { min: 2, max: 50, message: "合同编号长度必须介于 2 和 50 之间", trigger: "blur" }
  ],
  contractName: [
    { required: true, message: "合同名称不能为空", trigger: "blur" },
    { min: 2, max: 100, message: "合同名称长度必须介于 2 和 100 之间", trigger: "blur" }
  ],
  studentId: [
    { required: true, message: "学生姓名不能为空", trigger: "change" }
  ],
  companyName: [
    { required: true, message: "企业名称不能为空", trigger: "change" }
  ],
  postName: [
    { required: true, message: "岗位不能为空", trigger: "change" }
  ]
};

// 学生选项
const studentOptions = ref([]);

// 企业选项
const companyOptions = ref([]);

// 岗位选项（从API获取）
const postOptions = ref([]);

/** 获取岗位列表 */
function getPostList() {
  listPost({}).then(response => {
    postOptions.value = response.rows.map(item => ({
      id: item.postId,
      name: item.postName
    }));
  }).catch(error => {
    console.error('获取岗位列表失败:', error);
  });
}

/** 获取学生列表 */
function getStudentList() {
  listStudentInfo({ pageNum: 1, pageSize: 1000 }).then(response => {
    studentOptions.value = (response.rows || []).map(student => ({
      id: student.studentId,
      name: student.studentName,
      number: student.studentNumber
    }));
  }).catch(error => {
    console.error('获取学生列表失败:', error);
  });
}

/** 获取企业列表 */
function getCompanyList() {
  listCompany({ pageNum: 1, pageSize: 1000 }).then(response => {
    companyOptions.value = (response.rows || []).map(company => ({
      name: company.companyName
    }));
  }).catch(error => {
    console.error('获取企业列表失败:', error);
  });
}

/** 获取合同信息 */
function getInfo() {
  if (contractId.value) {
    loading.value = true;
    getContract(contractId.value).then(response => {
      contractInfo.value = response.data;
      form.value = { ...response.data };
      loading.value = false;
    }).catch(() => {
      loading.value = false;
    });
  }
}

/** 确认按钮操作（查看模式） */
function handleConfirm() {
  router.back();
}

/** 返回按钮操作 */
function handleBack() {
  router.back();
}

/** 提交表单 */
function handleSubmit() {
  // 验证基础信息表单
  proxy.$refs["contractRef"].validate(valid1 => {
    if (valid1) {
      // 验证岗位信息表单
      proxy.$refs["contractRef2"].validate(valid2 => {
        if (valid2) {
          loading.value = true;
          const isEditMode = pageMode.value === 'edit';
          if (isEditMode) {
            updateContract(form.value).then(response => {
              proxy.$modal.msgSuccess("修改成功");
              loading.value = false;
              router.back();
            }).catch(() => {
              loading.value = false;
            });
          } else {
            addContract(form.value).then(response => {
              proxy.$modal.msgSuccess("新增成功");
              loading.value = false;
              router.back();
            }).catch(() => {
              loading.value = false;
            });
          }
        }
      });
    }
  });
}

/** 取消操作 */
function handleCancel() {
  router.back();
}

/** 重置表单 */
function reset() {
  form.value = {
    contractId: null,
    contractNumber: '',
    contractName: '',
    signDate: '',
    studentId: null,
    companyName: '',
    postName: '',
    salary: null,
    remark: ''
  };
  proxy.resetForm("contractRef");
  // 重置岗位信息表单的验证状态
  if (proxy.$refs["contractRef2"]) {
    proxy.$refs["contractRef2"].resetFields();
  }
}

onMounted(() => {
  reset();
  getPostList(); // 获取岗位列表
  getStudentList(); // 获取学生列表
  getCompanyList(); // 获取企业列表
  if (pageMode.value === 'detail') {
    isViewMode.value = true;
    getInfo();
  } else if (pageMode.value === 'edit') {
    isViewMode.value = false;
    getInfo();
  }
  // add模式不需要获取数据
});
</script>

<style lang="scss" scoped>
.app-container {
  padding: 0;
  background: #f5f7fa;
  min-height: 100vh;
}

.page-header {
  display: flex;
  align-items: center;
  padding: 16px 24px;
  background: #fff;
  border-bottom: 1px solid #e6e6e6;
  margin-bottom: 16px;

  .back-btn {
    display: flex;
    align-items: center;
    gap: 4px;
    color: #666;
    font-size: 14px;
    margin-right: 16px;
    
    &:hover {
      color: #409eff;
    }
  }

  .page-title {
    font-size: 18px;
    font-weight: 600;
    color: #303133;
  }
}

.section-card {
  margin: 0 24px 16px 24px;
  border-radius: 8px;
  border: 1px solid #e6e6e6;

  .section-header {
    display: flex;
    align-items: center;
  }

  .section-title {
    font-size: 16px;
    font-weight: 600;
    color: #303133;
    position: relative;
    padding-left: 12px;

    &::before {
      content: '';
      position: absolute;
      left: 0;
      top: 50%;
      transform: translateY(-50%);
      width: 4px;
      height: 16px;
      background: #409eff;
      border-radius: 2px;
    }
  }
}

.view-content {
  padding: 24px;

  .view-item {
    display: flex;
    align-items: center;
    margin-bottom: 20px;
    min-height: 32px;

    .label {
      font-size: 14px;
      color: #606266;
      min-width: 100px;
      margin-right: 12px;
    }

    .value {
      font-size: 14px;
      color: #303133;
      flex: 1;
    }
  }
}

.form-content {
  padding: 24px;

  :deep(.el-form-item) {
    margin-bottom: 24px;
  }

  :deep(.el-form-item__label) {
    font-size: 14px;
    color: #606266;
  }

  :deep(.el-input__inner),
  :deep(.el-textarea__inner) {
    border-radius: 6px;
  }

  :deep(.el-select) {
    width: 100%;
  }

  .form-item {
    margin-bottom: 24px;
    
    .form-label {
      display: inline-block;
      font-size: 14px;
      color: #606266;
      min-width: 100px;
      margin-bottom: 8px;
    }
  }
}

.action-footer {
  display: flex;
  justify-content: center;
  gap: 16px;
  padding: 24px;
  background: #fff;
  border-top: 1px solid #e6e6e6;
  margin: 0 24px;
  border-radius: 0 0 8px 8px;

  .el-button {
    min-width: 88px;
    height: 40px;
    font-size: 14px;
    border-radius: 6px;
  }
}
</style> 