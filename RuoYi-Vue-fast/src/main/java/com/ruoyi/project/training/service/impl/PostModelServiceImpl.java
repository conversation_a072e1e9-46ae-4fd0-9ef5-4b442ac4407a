package com.ruoyi.project.training.service.impl;

import java.util.List;
import java.util.ArrayList;
import java.util.stream.Collectors;

import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.common.utils.SecurityUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import com.ruoyi.project.training.mapper.PostModelMapper;
import com.ruoyi.project.training.domain.PostModel;
import com.ruoyi.project.training.domain.PostModelConfig;
import com.ruoyi.project.training.domain.PostAbilityConfig;
import com.ruoyi.project.training.domain.PostCourseConfig;
import com.ruoyi.project.training.service.IPostModelService;
import com.ruoyi.project.training.service.IPostModelConfigService;
import com.ruoyi.project.organization.service.IOrgCompanyService;

/**
 * 岗位模型Service业务层处理
 * 
 * <AUTHOR>
 * @date 2024-01-01
 */
@Service
public class PostModelServiceImpl implements IPostModelService 
{
    @Autowired
    private PostModelMapper postModelMapper;

    @Autowired
    private IPostModelConfigService postModelConfigService;

    @Autowired
    private IOrgCompanyService orgCompanyService;

    /**
     * 查询岗位模型
     *
     * @param postId 岗位模型主键
     * @return 岗位模型
     */
    @Override
    public PostModel selectPostModelByPostId(Long postId)
    {
        PostModel postModel = postModelMapper.selectPostModelByPostId(postId);
        if (postModel != null) {
            // 查询关联的企业ID
            Long[] companyIds = postModelMapper.selectCompanyIdsByPostId(postId);
            if (companyIds != null && companyIds.length > 0) {
                List<Long> companyIdList = new ArrayList<>();
                for (Long companyId : companyIds) {
                    companyIdList.add(companyId);
                }
                postModel.setCompanyIds(companyIdList);

                // 生成企业名称显示字符串（使用mock数据映射）
                postModel.setCompanyNames(getCompanyNames(companyIdList));
            }
        }
        return postModel;
    }

    /**
     * 查询岗位模型列表
     *
     * @param postModel 岗位模型
     * @return 岗位模型
     */
    @Override
    public List<PostModel> selectPostModelList(PostModel postModel)
    {
        List<PostModel> list = postModelMapper.selectPostModelList(postModel);
        // 为每个岗位模型填充企业信息
        for (PostModel post : list) {
            Long[] companyIds = postModelMapper.selectCompanyIdsByPostId(post.getPostId());
            if (companyIds != null && companyIds.length > 0) {
                List<Long> companyIdList = new ArrayList<>();
                for (Long companyId : companyIds) {
                    companyIdList.add(companyId);
                }
                post.setCompanyIds(companyIdList);
                post.setCompanyNames(getCompanyNames(companyIdList));
            }
        }
        return list;
    }

    /**
     * 新增岗位模型
     *
     * @param postModel 岗位模型
     * @return 结果
     */
    @Override
    public int insertPostModel(PostModel postModel)
    {
        postModel.setCreateTime(DateUtils.getNowDate());
        int result = postModelMapper.insertPostModel(postModel);

        // 插入企业关联
        if (result > 0 && postModel.getCompanyIds() != null && !postModel.getCompanyIds().isEmpty()) {
            Long[] companyIds = postModel.getCompanyIds().toArray(new Long[0]);
            postModelMapper.insertPostModelCompany(postModel.getPostId(), companyIds);
        }

        return result;
    }

    /**
     * 修改岗位模型
     *
     * @param postModel 岗位模型
     * @return 结果
     */
    @Override
    public int updatePostModel(PostModel postModel)
    {
        postModel.setUpdateTime(DateUtils.getNowDate());
        int result = postModelMapper.updatePostModel(postModel);

        // 更新企业关联
        if (result > 0) {
            // 先删除原有关联
            postModelMapper.deletePostModelCompanyByPostId(postModel.getPostId());

            // 插入新的关联
            if (postModel.getCompanyIds() != null && !postModel.getCompanyIds().isEmpty()) {
                Long[] companyIds = postModel.getCompanyIds().toArray(new Long[0]);
                postModelMapper.insertPostModelCompany(postModel.getPostId(), companyIds);
            }
        }

        return result;
    }

    /**
     * 批量删除岗位模型
     *
     * @param postIds 需要删除的岗位模型主键
     * @return 结果
     */
    @Override
    @Transactional
    public int deletePostModelByPostIds(Long[] postIds)
    {
        // 先删除配置模型
        for (Long postId : postIds) {
            postModelConfigService.deletePostModelConfigByPostId(postId);
        }
        // 再删除关联关系
        postModelMapper.deletePostModelCompanyByPostIds(postIds);
        // 最后删除岗位模型
        return postModelMapper.deletePostModelByPostIds(postIds);
    }

    /**
     * 删除岗位模型信息
     *
     * @param postId 岗位模型主键
     * @return 结果
     */
    @Override
    @Transactional
    public int deletePostModelByPostId(Long postId)
    {
        // 先删除配置模型
        postModelConfigService.deletePostModelConfigByPostId(postId);
        // 再删除关联关系
        postModelMapper.deletePostModelCompanyByPostId(postId);
        // 最后删除岗位模型
        return postModelMapper.deletePostModelByPostId(postId);
    }

    /**
     * 复制岗位模型
     * 
     * @param postModel 岗位模型
     * @return 结果
     */
    @Override
    @Transactional
    public int copyPostModel(PostModel postModel)
    {
        // 根据原岗位ID查询原岗位信息
        PostModel originalPost = postModelMapper.selectPostModelByPostId(postModel.getPostId());
        if (originalPost != null) {
            // 创建新的岗位模型
            PostModel newPost = new PostModel();
            newPost.setPostName(postModel.getPostName()); // 使用新的岗位名称
            newPost.setPostDesc(originalPost.getPostDesc()); // 复制描述
            newPost.setCompanyIds(originalPost.getCompanyIds()); // 复制企业IDs
            newPost.setCreateTime(DateUtils.getNowDate());
            newPost.setCreateBy(SecurityUtils.getUsername());
            
            int result = postModelMapper.insertPostModel(newPost);
            
            if (result > 0) {
                // 复制企业关联
                if (originalPost.getCompanyIds() != null && !originalPost.getCompanyIds().isEmpty()) {
                    Long[] companyIds = originalPost.getCompanyIds().toArray(new Long[0]);
                    postModelMapper.insertPostModelCompany(newPost.getPostId(), companyIds);
                }
                
                // 复制配置模型
                copyPostModelConfig(originalPost.getPostId(), newPost.getPostId());
            }
            
            return result;
        }
        return 0;
    }
    
    /**
     * 复制岗位模型配置
     * 
     * @param originalPostId 原岗位ID
     * @param newPostId 新岗位ID
     */
    private void copyPostModelConfig(Long originalPostId, Long newPostId)
    {
        // 查询原岗位的配置模型
        PostModelConfig originalConfig = postModelConfigService.selectPostModelConfigByPostId(originalPostId);
        if (originalConfig != null && originalConfig.getAbilityConfigs() != null) {
            // 创建新的配置模型
            PostModelConfig newConfig = new PostModelConfig();
            newConfig.setPostId(newPostId);
            newConfig.setPostName(postModelMapper.selectPostModelByPostId(newPostId).getPostName());
            newConfig.setCreateTime(DateUtils.getNowDate());
            newConfig.setCreateBy(SecurityUtils.getUsername());
            
            // 复制职业能力配置
            List<PostAbilityConfig> newAbilityConfigs = new ArrayList<>();
            for (PostAbilityConfig originalAbility : originalConfig.getAbilityConfigs()) {
                PostAbilityConfig newAbility = new PostAbilityConfig();
                newAbility.setPostId(newPostId);
                newAbility.setAbilityId(originalAbility.getAbilityId());
                newAbility.setAbilityName(originalAbility.getAbilityName());
                newAbility.setThreshold(originalAbility.getThreshold());
                newAbility.setWeight(originalAbility.getWeight());
                newAbility.setCreateTime(DateUtils.getNowDate());
                newAbility.setCreateBy(SecurityUtils.getUsername());
                
                // 复制课程配置
                List<PostCourseConfig> newCourseConfigs = new ArrayList<>();
                if (originalAbility.getCourseConfigs() != null) {
                    for (PostCourseConfig originalCourse : originalAbility.getCourseConfigs()) {
                        PostCourseConfig newCourse = new PostCourseConfig();
                        newCourse.setPostId(newPostId);
                        newCourse.setAbilityConfigId(newAbility.getConfigId());
                        newCourse.setCourseId(originalCourse.getCourseId());
                        newCourse.setCourseName(originalCourse.getCourseName());
                        newCourse.setCourseObjective(originalCourse.getCourseObjective());
                        newCourse.setCourseType(originalCourse.getCourseType());
                        newCourse.setWeight(originalCourse.getWeight());
                        newCourse.setCreateTime(DateUtils.getNowDate());
                        newCourse.setCreateBy(SecurityUtils.getUsername());
                        newCourseConfigs.add(newCourse);
                    }
                }
                newAbility.setCourseConfigs(newCourseConfigs);
                newAbilityConfigs.add(newAbility);
            }
            newConfig.setAbilityConfigs(newAbilityConfigs);
            
            // 保存新的配置模型
            postModelConfigService.savePostModelConfig(newConfig);
        }
    }

    /**
     * 根据企业ID列表获取企业名称字符串（使用mock数据映射）
     *
     * @param companyIds 企业ID列表
     * @return 企业名称字符串，用"、"分隔
     */
    private String getCompanyNames(List<Long> companyIds) {
        if (companyIds == null || companyIds.isEmpty()) {
            return "";
        }

        // Mock企业数据映射
        java.util.Map<Long, String> companyMap = new java.util.HashMap<>();
        companyMap.put(1L, "企业名称1");
        companyMap.put(2L, "企业名称2");
        companyMap.put(3L, "企业名称3");
        companyMap.put(4L, "企业名称4");
        companyMap.put(5L, "企业名称5");

        return companyIds.stream()
                .map(companyMap::get)
                .filter(name -> name != null)
                .collect(Collectors.joining("、"));
    }
}
